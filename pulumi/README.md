# A1D Agent Azure 部署指南

这个 Pulumi 模板已经针对 A1D Agent AI 服务进行了优化配置，支持常驻运行和 AI 工作负载。

## 主要改进

### 🔄 常驻运行配置
- **最小副本数**: 1 (始终保持至少一个实例运行)
- **禁用缩放到零**: 确保服务始终可用
- **增强的健康检查**: 适配 AI 服务的启动时间

### 🚀 LLM Chat 应用优化
- **CPU**: 0.5 核心 (适合 LLM chat 应用，主要是 API 转发)
- **内存**: 1GB (足够 Node.js chat 应用使用)
- **端口**: 4111 (匹配应用的实际端口)
- **扩展规则**: 仅在高并发时触发 (>50 并发请求)

### 🔐 环境变量和密钥管理
- 自动配置 `POSTGRES_URL` 和 `CONVEX_URL`
- 支持通过 Pulumi 配置管理敏感信息
- 预留了 AI 服务集成的密钥配置

### 💡 为什么这样配置？
**针对 LLM Chat 应用的特点优化：**
- **最小扩展**: LLM chat 应用的主要计算在外部 API，多个进程实例意义不大
- **资源适中**: 0.5 CPU + 1GB 内存足够处理请求转发和业务逻辑
- **高并发阈值**: 设置 50 并发请求才扩展，因为 chat 应用可以处理很多并发连接
- **最多 2 实例**: 主要为了零停机部署，而不是性能扩展

## 快速开始

### 1. 设置必要的密钥

```bash
# 进入 pulumi 目录
cd pulumi

# 选择或创建 stack
pulumi stack select dev  # 或 pulumi stack init dev

# 🤖 自动设置环境变量 (推荐)
../scripts/auto-set-pulumi-env.sh

# 或手动设置必要的密钥
pulumi config set --secret postgresurl "postgresql://user:pass@host:port/database"
pulumi config set --secret convexurl "https://your-deployment.convex.cloud"
```

### 2. 可选：手动设置 AI 服务密钥

```bash
# 根据你的集成需求设置相应的 API 密钥 (如果没有使用自动化脚本)
pulumi config set --secret minimaxapikey "your-minimax-key"
pulumi config set --secret anthropicapikey "your-anthropic-key"
pulumi config set --secret groqapikey "your-groq-key"
# ... 其他 API 密钥
```

**注意**: 推荐使用自动化脚本 `../scripts/auto-set-pulumi-env.sh` 来设置所有环境变量。

### 3. 自定义配置 (可选)

```bash
# 自定义应用名称
pulumi config set appName "my-a1d-agent"

# 自定义资源配置
pulumi config set cpu 2.0
pulumi config set memory 4
pulumi config set maxReplicas 5

# 设置环境
pulumi config set environment "production"
```

### 4. 部署

使用提供的部署脚本：

```bash
# 从项目根目录运行
./scripts/deploy-a1d-agent.sh
```

或手动部署：

```bash
cd pulumi
pulumi up
```

## 配置参数

| 参数 | 默认值 | 说明 |
|------|--------|------|
| `appName` | `a1d-agent` | 应用名称 |
| `containerImage` | `a1dazureacr.azurecr.io/a1d-agent:latest` | 容器镜像 |
| `cpu` | `0.5` | CPU 核心数 |
| `memory` | `1` | 内存 (GB) |
| `port` | `4111` | 应用端口 |
| `minReplicas` | `1` | 最小副本数 |
| `maxReplicas` | `2` | 最大副本数 |
| `scaleToZero` | `false` | 是否允许缩放到零 |

## 健康检查

应用配置了三种健康检查：

- **Startup Probe**: 启动检查，允许最多 2 分钟启动时间
- **Liveness Probe**: 存活检查，每 30 秒检查一次
- **Readiness Probe**: 就绪检查，每 15 秒检查一次

所有检查都使用 `/health` 端点。

## 扩展规则

- **HTTP 并发**: 超过 50 个并发请求时扩展 (LLM chat 应用可以处理更多并发连接)
- **移除 CPU 扩展**: LLM chat 应用的主要计算在外部 API，不需要 CPU 扩展

## 输出信息

部署完成后，你将获得以下端点：

- **健康检查**: `https://{appName}.whiteboardanimation.ai/health`
- **Mastra API**: `https://{appName}.whiteboardanimation.ai/mastra`
- **主要 URL**: `https://{appName}.whiteboardanimation.ai`

## 故障排除

### 常见问题

1. **部署失败**: 检查是否设置了所有必要的密钥
2. **健康检查失败**: 确保应用在端口 4111 上提供 `/health` 端点
3. **内存不足**: 考虑增加 `memory` 配置

### 查看日志

```bash
# 查看部署状态
pulumi stack output

# 查看 Azure 容器应用日志
az containerapp logs show --name {appName} --resource-group {resourceGroup}
```
